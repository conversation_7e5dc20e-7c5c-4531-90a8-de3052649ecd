//文华财经K线计数指标
//功能：显示每日K线数量计数

//参数设置
PARAMS
    显示间隔(2,1,10,1);  //每隔几根K线显示一次计数
END

//变量定义
VAR
    当日计数: 0;
    前一日期: 0;
    当前日期: 0;
END

//主要逻辑
BEGIN
    当前日期 := DATE;

    //判断是否为新的一天
    IF 前一日期 <> 当前日期 THEN
    BEGIN
        当日计数 := 1;
        前一日期 := 当前日期;
    END
    ELSE
    BEGIN
        当日计数 := 当日计数 + 1;
    END;

    //按设定间隔显示计数
    IF 当日计数 MOD 显示间隔 = 0 THEN
    BEGIN
        DRAWTEXT(当日计数, LOW * 0.998, NUMTOSTR(当日计数,0));
    END;

END